import type { FormConfig, FormField } from '@/types';

// 新建联系人表单配置
export const newContactFormConfig: FormConfig = {
    layout: {
        labelPosition: 'right',
        labelWidth: '100px',
        size: 'default',
        gutter: 20
    },
    fields: [
        {
            field: 'name',
            label: '联系人姓名',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            maxLength: 50,
            showWordLimit: true,
            placeholder: '请输入联系人姓名',
            prefixIcon: 'User',
            rules: [
                { required: true, message: '请输入联系人姓名', trigger: 'blur' },
                { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
            ]
        },
        {
            field: 'position',
            label: '职位',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            placeholder: '请输入职位',
            prefixIcon: 'Briefcase'
        },
        {
            field: 'customerId',
            label: '所属客户',
            type: 'select',
            colSpan: 12,
            required: true,
            clearable: true,
            filterable: true,
            placeholder: '请选择所属客户',
            options: [] // 需要动态获取客户列表
        },
        {
            field: 'phone',
            label: '联系电话',
            type: 'input',
            colSpan: 12,
            required: true,
            clearable: true,
            placeholder: '请输入联系电话',
            prefixIcon: 'Phone',
            rules: [
                { required: true, message: '请输入联系电话', trigger: 'blur' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
            ]
        },
        {
            field: 'email',
            label: '电子邮箱',
            type: 'input',
            colSpan: 12,
            clearable: true,
            placeholder: '请输入电子邮箱',
            prefixIcon: 'Message',
            rules: [
                { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
            ]
        },
        {
            field: 'responsiblePerson',
            label: '负责人',
            type: 'select',
            colSpan: 12,
            required: true,
            options: [] // 需要从API获取用户列表
        },
        {
            field: 'remarks',
            label: '备注',
            type: 'textarea',
            colSpan: 24,
            clearable: true,
            placeholder: '请输入备注信息',
            maxLength: 500,
            showWordLimit: true,
            rows: 4
        }
    ] as FormField[]
}; 