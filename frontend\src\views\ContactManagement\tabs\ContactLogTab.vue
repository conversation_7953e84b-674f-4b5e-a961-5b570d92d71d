<template>
    <div class="contact-log-tab">
        <!-- 筛选区域 -->
        <div class="filter-section">
            <el-card class="filter-card">
                <el-form :model="filterForm" inline size="default">
                    <el-form-item label="操作类型">
                        <el-select v-model="filterForm.operationType" placeholder="全部类型" clearable style="width: 150px">
                            <el-option 
                                v-for="option in OPERATION_TYPES" 
                                :key="option.value" 
                                :label="option.label" 
                                :value="option.value" 
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="操作人">
                        <el-select v-model="filterForm.operatorId" placeholder="全部操作人" clearable style="width: 150px">
                            <el-option 
                                v-for="user in userOptions" 
                                :key="user.value" 
                                :label="user.label" 
                                :value="user.value" 
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="时间范围">
                        <el-date-picker
                            v-model="filterForm.dateRange"
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            style="width: 300px"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleFilter">
                            <el-icon><Search /></el-icon>
                            查询
                        </el-button>
                        <el-button @click="resetFilter">
                            <el-icon><Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>

        <!-- 日志列表 -->
        <div class="log-list-section">
            <el-card class="list-card">
                <template #header>
                    <div class="card-header">
                        <el-icon class="header-icon"><Document /></el-icon>
                        <span>操作日志 ({{ pagination.total }})</span>
                        <div class="header-actions">
                            <el-button size="small" @click="refreshLogs">
                                <el-icon><Refresh /></el-icon>
                                刷新
                            </el-button>
                        </div>
                    </div>
                </template>
                
                <div class="log-container" v-loading="loadingLogs">
                    <div v-if="logs.length === 0" class="empty-state">
                        <el-empty description="暂无操作日志" />
                    </div>
                    
                    <el-timeline v-else>
                        <el-timeline-item
                            v-for="(log, index) in logs"
                            :key="log.id || index"
                            :timestamp="formatDate(log.operationTime)"
                            placement="top"
                            :color="getOperationColor(log.operationType)"
                            :icon="getOperationIcon(log.operationType)"
                            size="large"
                        >
                            <el-card class="log-item-card" :body-style="{ padding: '16px' }">
                                <div class="log-header">
                                    <div class="operation-info">
                                        <el-tag :type="getOperationTagType(log.operationType)" size="small">
                                            {{ getOperationTypeLabel(log.operationType) }}
                                        </el-tag>
                                        <span class="operator">{{ log.operatorName || log.operationBy || '系统' }}</span>
                                    </div>
                                    <div class="operation-time">
                                        {{ formatRelativeTime(log.operationTime) }}
                                    </div>
                                </div>
                                
                                <div class="log-content">
                                    <div class="operation-description">
                                        {{ log.operationDesc || log.operationDescription || log.operationContent }}
                                    </div>
                                    
                                    <!-- 详细变更信息 -->
                                    <div v-if="log.changeDetails" class="change-details">
                                        <el-collapse>
                                            <el-collapse-item title="查看详细变更" name="details">
                                                <div class="change-item" v-for="(change, key) in parseChangeDetails(log.changeDetails)" :key="key">
                                                    <div class="field-name">{{ change.fieldName }}:</div>
                                                    <div class="field-change">
                                                        <span class="old-value">{{ change.oldValue || '空' }}</span>
                                                        <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                                                        <span class="new-value">{{ change.newValue || '空' }}</span>
                                                    </div>
                                                </div>
                                            </el-collapse-item>
                                        </el-collapse>
                                    </div>
                                </div>
                                
                                <div class="log-meta">
                                    <span class="ip-address" v-if="log.ipAddress">
                                        <el-icon><Monitor /></el-icon>
                                        {{ log.ipAddress }}
                                    </span>
                                    <span class="user-agent" v-if="log.userAgent" :title="log.userAgent">
                                        <el-icon><Platform /></el-icon>
                                        {{ getBrowserInfo(log.userAgent) }}
                                    </span>
                                </div>
                            </el-card>
                        </el-timeline-item>
                    </el-timeline>
                    
                    <!-- 分页 -->
                    <div class="pagination-container" v-if="logs.length > 0">
                        <el-pagination
                            v-model:current-page="pagination.current"
                            v-model:page-size="pagination.size"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="pagination.total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { ContactOperation, UserOption } from '@/types/contact';
import { OPERATION_TYPES } from '@/types/contact';
import { formatRelativeTime, formatTime } from '@/utils/date';
import { getContactOperationLogs } from '@/views/ContactManagement/api';
import { ContactEntity } from '@/views/ContactManagement/types';
import {
    ArrowRight,
    Delete,
    Document,
    Edit,
    Monitor,
    Platform,
    Plus,
    Refresh,
    Search,
    View
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref, watch } from 'vue';

interface Props {
    entityData: ContactEntity;
    userOptions?: UserOption[];
}

defineOptions({
    name: 'ContactLogTab'
});

import { defineEmits, defineProps } from 'vue';

const emit = defineEmits<{
    (e: 'update:entity', value: Record<string, any>): void;
    (e: 'update-count', count: number): void;
}>();

const props = defineProps<Props>();

// 状态管理
const logs = ref<ContactOperation[]>([]);
const loadingLogs = ref(false);
const userOptions = ref<UserOption[]>(props.userOptions || []);

// 筛选表单
const filterForm = reactive({
    operationType: '',
    operatorId: '',
    dateRange: [] as [Date, Date] | []
});

// 分页信息
const pagination = reactive({
    current: 1,
    size: 20,
    total: 0
});

// 加载操作日志函数（需要在 resetFilter 之前定义）
const loadLogs = async () => {
    if (!props.entityData.id) return;
    
    try {
        loadingLogs.value = true;
        const params = {
            contactId: props.entityData.id,
            current: pagination.current,
            size: pagination.size,
            operationType: filterForm.operationType || undefined,
            operatorId: filterForm.operatorId || undefined,
            startTime: filterForm.dateRange?.[0]?.toISOString(),
            endTime: filterForm.dateRange?.[1]?.toISOString()
        };
        
        console.log('Loading logs with params:', params);
        const response = await getContactOperationLogs(props.entityData.id, params);
        
        if (response.code === 200 && response.data) {
            logs.value = response.data.records || [];
            pagination.total = response.data.total || 0;
            
            // 更新计数
            emit('update-count', pagination.total);
            
            console.log('Loaded logs:', logs.value);
        } else {
            console.error('Failed to load logs:', response);
            ElMessage.error(response.msg || '获取操作日志失败');
        }
    } catch (error) {
        console.error('Error loading logs:', error);
        ElMessage.error('获取操作日志失败');
    } finally {
        loadingLogs.value = false;
    }
};

// 重置筛选函数（需要在 watch 之前定义）
const resetFilter = () => {
    Object.assign(filterForm, {
        operationType: '',
        operatorId: '',
        dateRange: []
    });
    pagination.current = 1;
    loadLogs();
};

// 监听联系人变化
watch(
    () => props.entityData.id,
    (newId) => {
        if (newId) {
            resetFilter();
        }
    },
    { immediate: true }
);

// 监听用户选项变化
watch(
    () => props.userOptions,
    (newOptions) => {
        if (newOptions) {
            userOptions.value = newOptions;
        }
    },
    { immediate: true }
);

// 组件挂载时加载数据
onMounted(() => {
    if (props.entityData.id) {
        loadLogs(); // 这里调用前面定义好的函数
    }
});

// loadLogs 函数已移动到前面定义

// 处理筛选
const handleFilter = () => {
    pagination.current = 1;
    loadLogs();
};

// resetFilter 函数已移动到前面定义

// 刷新日志
const refreshLogs = () => {
    loadLogs();
};

// 分页大小变化
const handleSizeChange = (size: number) => {
    pagination.size = size;
    pagination.current = 1;
    loadLogs();
};

// 当前页变化
const handleCurrentChange = (current: number) => {
    pagination.current = current;
    loadLogs();
};

// 格式化日期
const formatDate = (dateStr: string | undefined) => {
    if (!dateStr) return '-';
    return formatTime(dateStr);
};

// 获取操作类型颜色
const getOperationColor = (type: string) => {
    const colorMap: Record<string, string> = {
        create: '#67C23A',
        update: '#409EFF',
        delete: '#F56C6C',
        view: '#909399',
        follow: '#E6A23C',
        unfollow: '#909399'
    };
    return colorMap[type] || '#909399';
};

// 获取操作类型图标
const getOperationIcon = (type: string) => {
    const iconMap: Record<string, any> = {
        create: Plus,
        update: Edit,
        delete: Delete,
        view: View,
        follow: Plus,
        unfollow: Delete
    };
    return iconMap[type] || Document;
};

// 获取操作类型标签类型
const getOperationTagType = (type: string): any => {
    const typeMap: Record<string, string> = {
        create: 'success',
        update: 'primary',
        delete: 'danger',
        view: 'info',
        follow: 'warning',
        unfollow: 'info'
    };
    return typeMap[type] || 'info';
};

// 获取操作类型标签
const getOperationTypeLabel = (type: string) => {
    const option = OPERATION_TYPES.find(item => item.value === type);
    return option ? option.label : type;
};

// 解析变更详情
const parseChangeDetails = (details: string) => {
    try {
        const changes = JSON.parse(details);
        return Object.keys(changes).map(key => ({
            fieldName: getFieldDisplayName(key),
            oldValue: changes[key].oldValue,
            newValue: changes[key].newValue
        }));
    } catch (error) {
        return [];
    }
};

// 获取字段显示名称
const getFieldDisplayName = (fieldName: string) => {
    const fieldMap: Record<string, string> = {
        name: '姓名',
        phone: '电话',
        email: '邮箱',
        company: '公司',
        position: '职位',
        department: '部门',
        address: '地址',
        remark: '备注',
        isFollowing: '关注状态',
        assignedTo: '负责人'
    };
    return fieldMap[fieldName] || fieldName;
};

// 获取浏览器信息
const getBrowserInfo = (userAgent: string) => {
    if (!userAgent) return '未知';
    
    if (userAgent.includes('Chrome')) {
        return 'Chrome';
    } else if (userAgent.includes('Firefox')) {
        return 'Firefox';
    } else if (userAgent.includes('Safari')) {
        return 'Safari';
    } else if (userAgent.includes('Edge')) {
        return 'Edge';
    } else {
        return '其他浏览器';
    }
};
</script>

<style scoped>
.contact-log-tab {
    padding: 20px;
}

.filter-section {
    margin-bottom: 24px;
}

.filter-card,
.list-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
}

.header-icon {
    font-size: 18px;
    color: var(--el-color-primary);
}

.header-actions {
    margin-left: auto;
}

.log-container {
    min-height: 300px;
}

.empty-state {
    text-align: center;
    padding: 40px 0;
}

.log-item-card {
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    margin-bottom: 0;
}

.log-item-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.operation-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.operator {
    color: #666;
    font-size: 13px;
}

.operation-time {
    color: #999;
    font-size: 12px;
}

.log-content {
    margin-bottom: 12px;
}

.operation-description {
    color: #333;
    line-height: 1.6;
    margin-bottom: 8px;
}

.change-details {
    margin-top: 8px;
}

.change-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
}

.field-name {
    font-weight: 500;
    color: #666;
    min-width: 80px;
}

.field-change {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.old-value {
    color: #F56C6C;
    text-decoration: line-through;
}

.new-value {
    color: #67C23A;
    font-weight: 500;
}

.arrow-icon {
    color: #909399;
    font-size: 12px;
}

.log-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
    padding-top: 8px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.ip-address,
.user-agent {
    display: flex;
    align-items: center;
    gap: 4px;
}

.user-agent {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.pagination-container {
    margin-top: 24px;
    text-align: center;
}

:deep(.el-timeline-item__timestamp) {
    font-weight: 500;
    color: #666;
}

:deep(.el-timeline-item__node) {
    border-width: 3px;
}

:deep(.el-collapse-item__header) {
    font-size: 13px;
    color: #666;
}

@media (max-width: 768px) {
    .contact-log-tab {
        padding: 12px;
    }
    
    .log-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .log-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .change-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .field-change {
        width: 100%;
    }
}
</style>