// 联系人实体接口
export interface ContactEntity {
    id: number;
    name: string;
    position?: string;
    phone?: string;
    mobile?: string; // 手机号
    telephone?: string; // 固定电话
    email?: string;
    customerId?: number;
    customerName?: string;
    responsiblePersonId?: string;
    delFlag: string;
    createTime?: string;
    createBy?: string;
    updateTime?: string;
    updateBy?: string;
    remarks?: string;
    isFollowing?: boolean; // 是否关注该联系人
    // 新增详细信息字段
    gender?: string; // 性别
    birthday?: string; // 生日
    address?: string; // 地址
    department?: string; // 部门
    decisionRole?: string; // 决策角色
    contactLevel?: string; // 联系人级别
    nextContactTime?: string; // 下次联系时间
    status?: string; // 状态
    customerInfo?: any; // 客户信息
}

// 客户选项接口
export interface CustomerOption {
    value: string | number;
    label: string;
}

// 负责人选项接口
export interface ResponsiblePersonOption {
    value: string | number;
    label: string;
}

export const DEFAULT_CONTACT: ContactEntity = {
    id: 0,
    name: '',
    position: '',
    phone: '',
    email: '',
    customerId: 0,
    customerName: '',
    responsiblePersonId: '',
    delFlag: '0',
    createTime: ''
}; 